# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "522e9443458a19ffd2ee5bfe10ffb22d"
name = "checkout-app-ext-only"
application_url = "https://shopify.dev/apps/default-app-home"
embedded = true

[build]
automatically_update_urls_on_dev = true
include_config_on_deploy = true

[webhooks]
api_version = "2026-01"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
# Required scopes for address autocomplete functionality
scopes = "read_customers,read_orders"

[auth]
redirect_urls = [ "https://shopify.dev/apps/default-app-home/api/auth" ]
