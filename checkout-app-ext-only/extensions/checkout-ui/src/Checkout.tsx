import { extension } from "@shopify/ui-extensions/checkout";

// Статические данные городов Израиля для тестирования
const ISRAEL_CITIES = [
  {
    id: "tel-aviv-1",
    name: "Tel Aviv",
    nameHe: "תל אביב",
    zip: "6100000",
    district: "Tel Aviv"
  },
  {
    id: "jerusalem-1",
    name: "Jerusalem",
    nameHe: "ירושלים",
    zip: "9100000",
    district: "Jerusalem"
  },
  {
    id: "haifa-1",
    name: "Haifa",
    nameHe: "חיפה",
    zip: "3100000",
    district: "Haifa"
  },
  {
    id: "rishon-lezion-1",
    name: "Rishon LeZion",
    nameHe: "ראשון לציון",
    zip: "7500000",
    district: "Central"
  },
  {
    id: "petah-tikva-1",
    name: "<PERSON><PERSON>",
    nameHe: "פתח תקווה",
    zip: "4900000",
    district: "Central"
  },
  {
    id: "ashdod-1",
    name: "<PERSON><PERSON><PERSON>",
    nameHe: "אשדוד",
    zip: "7700000",
    district: "Southern"
  },
  {
    id: "netanya-1",
    name: "Netanya",
    nameHe: "נתניה",
    zip: "4200000",
    district: "Central"
  },
  {
    id: "beer-sheva-1",
    name: "Beer Sheva",
    nameHe: "באר שבע",
    zip: "8400000",
    district: "Southern"
  },
  {
    id: "holon-1",
    name: "Holon",
    nameHe: "חולון",
    zip: "5800000",
    district: "Tel Aviv"
  },
  {
    id: "bnei-brak-1",
    name: "Bnei Brak",
    nameHe: "בני ברק",
    zip: "5100000",
    district: "Tel Aviv"
  },
  {
    id: "ramat-gan-1",
    name: "Ramat Gan",
    nameHe: "רמת גן",
    zip: "5200000",
    district: "Tel Aviv"
  },
  {
    id: "ashkelon-1",
    name: "Ashkelon",
    nameHe: "אשקלון",
    zip: "7800000",
    district: "Southern"
  },
  {
    id: "rehovot-1",
    name: "Rehovot",
    nameHe: "רחובות",
    zip: "7600000",
    district: "Central"
  },
  {
    id: "bat-yam-1",
    name: "Bat Yam",
    nameHe: "בת ים",
    zip: "5900000",
    district: "Tel Aviv"
  },
  {
    id: "beit-shemesh-1",
    name: "Beit Shemesh",
    nameHe: "בית שמש",
    zip: "9900000",
    district: "Jerusalem"
  },
  {
    id: "kfar-saba-1",
    name: "Kfar Saba",
    nameHe: "כפר סבא",
    zip: "4400000",
    district: "Central"
  },
  {
    id: "herzliya-1",
    name: "Herzliya",
    nameHe: "הרצליה",
    zip: "4600000",
    district: "Central"
  },
  {
    id: "hadera-1",
    name: "Hadera",
    nameHe: "חדרה",
    zip: "3800000",
    district: "Haifa"
  },
  {
    id: "modiin-1",
    name: "Modiin",
    nameHe: "מודיעין",
    zip: "7170000",
    district: "Central"
  },
  {
    id: "nazareth-1",
    name: "Nazareth",
    nameHe: "נצרת",
    zip: "1610000",
    district: "Northern"
  }
];

// Функция для поиска городов по запросу
function searchCities(query: string, field: string, countryCode?: string) {
  // Проверяем, что это Израиль
  if (countryCode !== 'IL') {
    return [];
  }

  if (!query || query.length < 2) {
    return [];
  }

  const searchTerm = query.toLowerCase();

  return ISRAEL_CITIES.filter(city => {
    // Поиск по английскому названию (используем indexOf для совместимости)
    const matchesEnglish = city.name.toLowerCase().indexOf(searchTerm) !== -1;
    // Поиск по еврейскому названию
    const matchesHebrew = city.nameHe.indexOf(query) !== -1;
    // Поиск по почтовому индексу
    const matchesZip = field === 'zip' && city.zip.indexOf(query) === 0;

    return matchesEnglish || matchesHebrew || matchesZip;
  }).slice(0, 5); // Ограничиваем до 5 результатов
}

// Функция для создания предложений адресов
function createAddressSuggestions(cities: any[], query: string, _field: string) {
  return cities.map(city => {
    const label = `${city.name} (${city.nameHe}), ${city.district}, Israel`;

    // Находим совпадающие подстроки для подсветки
    const matchedSubstrings: Array<{offset: number, length: number}> = [];
    const queryLower = query.toLowerCase();
    const labelLower = label.toLowerCase();
    const startIndex = labelLower.indexOf(queryLower);

    if (startIndex !== -1) {
      matchedSubstrings.push({
        offset: startIndex,
        length: query.length
      });
    }

    return {
      id: city.id,
      label: label,
      matchedSubstrings: matchedSubstrings,
      formattedAddress: {
        city: city.name,
        zip: city.zip,
        countryCode: 'IL' as const,
        provinceCode: city.district
      }
    };
  });
}

// Основная функция расширения для автозаполнения адресов
export default extension(
  "purchase.address-autocomplete.suggest",
  async ({ signal: _signal, target }) => {
    console.log('Address autocomplete extension started for Israel');

    const { field, value, selectedCountryCode } = target;

    console.log('Field:', field, 'Value:', value, 'Country:', selectedCountryCode);

    // Временно закомментированный запрос к внешнему API
    /*
    try {
      const response = await fetch(
        `https://your-api.com/address-suggestions?query=${encodeURIComponent(value)}&field=${field}&country=${selectedCountryCode}`,
        { signal: _signal }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return { suggestions: data.suggestions || [] };
    } catch (error) {
      console.error('Error fetching address suggestions:', error);
      // Fallback to static data
    }
    */

    // Используем статические данные городов Израиля
    try {
      const matchingCities = searchCities(value, field, selectedCountryCode);
      const suggestions = createAddressSuggestions(matchingCities, value, field);

      console.log('Found suggestions:', suggestions.length);

      return {
        suggestions: suggestions
      };
    } catch (error) {
      console.error('Error in address autocomplete:', error);
      return {
        suggestions: []
      };
    }
  }
);
