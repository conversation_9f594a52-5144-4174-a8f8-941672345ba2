# Инструкции по тестированию приложения автозаполнения адресов для Израиля

## ✅ Что было исправлено

1. **Исправлена структура кода**:
   - Удален дублированный `export default`
   - Использован правильный API `extension` из `@shopify/ui-extensions/checkout`
   - Исправлены TypeScript ошибки

2. **Добавлены статические данные**:
   - 20+ крупных городов Израиля
   - Названия на английском и иврите
   - Почтовые индексы
   - Районы/области

3. **Улучшена функциональность**:
   - Поиск по английскому и ивритскому названиям
   - Поиск по почтовому индексу
   - Подсветка совпадающих подстрок
   - Ограничение до 5 результатов

4. **Обновлена конфигурация**:
   - Включен `network_access` для будущих API запросов
   - Обновлены файлы локализации
   - Исправлено название расширения

## 🧪 Как тестировать

### Шаг 1: Откройте предварительный просмотр
Приложение уже запущено. Откройте ссылку:
https://visit-conditioning-landscapes-including.trycloudflare.com/extensions/dev-console

### Шаг 2: Найдите расширение
В консоли разработчика найдите расширение "israel-address-autocomplete"

### Шаг 3: Протестируйте на checkout
1. Перейдите в предварительный просмотр checkout
2. Выберите страну "Israel" (IL)
3. Начните вводить в поле адреса

### Шаг 4: Тестовые запросы

**Поиск по английскому названию:**
- Введите "Tel" → должен показать "Tel Aviv (תל אביב), Tel Aviv, Israel"
- Введите "Jerusalem" → должен показать "Jerusalem (ירושלים), Jerusalem, Israel"
- Введите "Haifa" → должен показать "Haifa (חיפה), Haifa, Israel"

**Поиск по ивритскому названию:**
- Введите "תל" → должен показать Тель-Авив
- Введите "ירו" → должен показать Иерусалим
- Введите "חי" → должен показать Хайфу

**Поиск по почтовому индексу:**
- Переключитесь на поле "Postal Code"
- Введите "610" → должен показать города с индексом 6100000
- Введите "910" → должен показать Иерусалим (9100000)

### Шаг 5: Проверьте автозаполнение
При выборе предложения должны автоматически заполниться:
- City (название города)
- Postal Code (почтовый индекс)
- Province/State (район)
- Country (Israel)

## 🔧 Структура проекта

```
extensions/checkout-ui/
├── src/
│   └── Checkout.tsx          # Основной код расширения
├── locales/
│   ├── en.default.json       # Английская локализация
│   ├── fr.json              # Французская локализация
│   └── he.json              # Ивритская локализация
├── shopify.extension.toml    # Конфигурация расширения
└── README.md                # Документация
```

## 🚀 Следующие шаги

1. **Интеграция с внешним API**:
   - Раскомментируйте блок с fetch запросом
   - Замените URL на реальный API адресов Израиля
   - Добавьте обработку ошибок

2. **Расширение данных**:
   - Добавьте больше городов
   - Включите улицы и номера домов
   - Добавьте координаты для карт

3. **Улучшение UX**:
   - Добавьте индикатор загрузки
   - Улучшите сообщения об ошибках
   - Добавьте дебаунсинг для запросов

## 📝 Логи и отладка

Расширение выводит логи в консоль браузера:
- "Address autocomplete extension started for Israel"
- "Field: [поле], Value: [значение], Country: [страна]"
- "Found suggestions: [количество]"

Откройте Developer Tools в браузере для просмотра логов.

## ⚠️ Важные замечания

1. **Требования Shopify Plus**: Checkout UI Extensions доступны только для Shopify Plus
2. **Тестовая среда**: Используйте development store с включенной функцией Checkout Extensibility
3. **Производственное развертывание**: Перед публикацией замените статические данные на реальный API

## 🎯 Ожидаемый результат

При правильной настройке вы должны увидеть:
- Выпадающий список с предложениями адресов при вводе
- Подсветку совпадающих частей текста
- Автоматическое заполнение полей при выборе адреса
- Работу на английском и иврите
